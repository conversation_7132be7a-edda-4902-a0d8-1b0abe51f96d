import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/types';

type ProductDetailsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'ProductDetails'>;
type ProductDetailsScreenRouteProp = RouteProp<RootStackParamList, 'ProductDetails'>;

const ProductDetailsScreen = () => {
  const navigation = useNavigation<ProductDetailsScreenNavigationProp>();
  const route = useRoute<ProductDetailsScreenRouteProp>();
  const [quantity, setQuantity] = useState(1);

  // Mock product data - in real app, fetch based on route.params.productId
  const product = {
    id: route.params.productId,
    name: 'Premium Fresh Avocado',
    price: 'KSh 250',
    originalPrice: 'KSh 300',
    image: '🥑',
    description: 'Premium quality fresh avocados sourced directly from local farms. Rich in healthy fats and nutrients, perfect for your daily meals.',
    weight: '1kg',
    origin: 'Kenya',
    category: 'Fruits',
    inStock: true,
    rating: 4.8,
    reviews: 124
  };

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    // TODO: Implement add to cart functionality
    console.log(`Added ${quantity} ${product.name} to cart`);
    navigation.navigate('Cart');
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6 flex-row items-center justify-between">
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
        >
          <Text className="text-gray-600 text-lg">←</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Product Details</Text>
        <TouchableOpacity className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
          <Text className="text-gray-600 text-lg">♡</Text>
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1">
        {/* Product Image */}
        <View className="items-center py-8 bg-gray-50 mx-6 rounded-2xl mb-6">
          <Text className="text-8xl">{product.image}</Text>
        </View>

        <View className="px-6">
          {/* Product Info */}
          <View className="mb-6">
            <Text className="text-2xl font-bold text-gray-800 mb-2">{product.name}</Text>
            <View className="flex-row items-center mb-3">
              <Text className="text-2xl font-bold text-emerald-600 mr-3">{product.price}</Text>
              <Text className="text-lg text-gray-400 line-through">{product.originalPrice}</Text>
            </View>
            <View className="flex-row items-center">
              <Text className="text-yellow-500 mr-2">⭐</Text>
              <Text className="text-gray-600">{product.rating} ({product.reviews} reviews)</Text>
            </View>
          </View>

          {/* Product Details */}
          <View className="mb-6">
            <Text className="text-lg font-semibold text-gray-800 mb-3">Product Details</Text>
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Weight</Text>
                <Text className="text-gray-800 font-medium">{product.weight}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Origin</Text>
                <Text className="text-gray-800 font-medium">{product.origin}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Category</Text>
                <Text className="text-gray-800 font-medium">{product.category}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Availability</Text>
                <Text className="text-emerald-600 font-medium">
                  {product.inStock ? 'In Stock' : 'Out of Stock'}
                </Text>
              </View>
            </View>
          </View>

          {/* Description */}
          <View className="mb-8">
            <Text className="text-lg font-semibold text-gray-800 mb-3">Description</Text>
            <Text className="text-gray-600 leading-6">{product.description}</Text>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Section */}
      <View className="bg-white border-t border-gray-200 px-6 py-4">
        {/* Quantity Selector */}
        <View className="flex-row items-center justify-between mb-4">
          <Text className="text-lg font-semibold text-gray-800">Quantity</Text>
          <View className="flex-row items-center">
            <TouchableOpacity
              className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
              onPress={() => handleQuantityChange(-1)}
            >
              <Text className="text-gray-600 text-xl font-bold">-</Text>
            </TouchableOpacity>
            <Text className="mx-6 text-xl font-semibold text-gray-800">{quantity}</Text>
            <TouchableOpacity
              className="w-10 h-10 bg-emerald-600 rounded-full items-center justify-center"
              onPress={() => handleQuantityChange(1)}
            >
              <Text className="text-white text-xl font-bold">+</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Add to Cart Button */}
        <TouchableOpacity
          className="bg-emerald-600 py-4 rounded-xl"
          onPress={handleAddToCart}
        >
          <Text className="text-white text-lg font-semibold text-center">
            Add to Cart • {product.price}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ProductDetailsScreen;
