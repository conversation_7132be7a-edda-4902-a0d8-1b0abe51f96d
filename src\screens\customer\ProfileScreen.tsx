import React from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';

type ProfileScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Profile'>;

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();

  const userInfo = {
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+254 712 345 678',
    address: 'Haraka Estate, Block A, House 12'
  };

  const menuItems = [
    { id: 1, title: 'Order History', icon: '📦', action: () => {} },
    { id: 2, title: 'Delivery Addresses', icon: '📍', action: () => {} },
    { id: 3, title: 'Payment Methods', icon: '💳', action: () => {} },
    { id: 4, title: 'Notifications', icon: '🔔', action: () => {} },
    { id: 5, title: 'Help & Support', icon: '❓', action: () => {} },
    { id: 6, title: 'About JujaFresh', icon: 'ℹ️', action: () => {} },
  ];

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', onPress: () => navigation.navigate('Welcome') }
      ]
    );
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6 flex-row items-center justify-between">
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
        >
          <Text className="text-gray-600 text-lg">←</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Profile</Text>
        <TouchableOpacity>
          <Text className="text-emerald-600 font-semibold">Edit</Text>
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1 px-6">
        {/* User Info */}
        <View className="items-center mb-8">
          <View className="w-24 h-24 bg-emerald-600 rounded-full items-center justify-center mb-4">
            <Text className="text-white text-3xl">👤</Text>
          </View>
          <Text className="text-2xl font-bold text-gray-800 mb-1">{userInfo.name}</Text>
          <Text className="text-gray-600">{userInfo.email}</Text>
        </View>

        {/* User Details */}
        <View className="bg-gray-50 rounded-2xl p-4 mb-6">
          <View className="space-y-4">
            <View className="flex-row items-center">
              <Text className="text-gray-600 w-20">Phone:</Text>
              <Text className="text-gray-800 font-medium">{userInfo.phone}</Text>
            </View>
            <View className="flex-row items-start">
              <Text className="text-gray-600 w-20">Address:</Text>
              <Text className="text-gray-800 font-medium flex-1">{userInfo.address}</Text>
            </View>
          </View>
        </View>

        {/* Menu Items */}
        <View className="space-y-2 mb-8">
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.id}
              className="bg-white border border-gray-200 rounded-xl p-4"
              onPress={item.action}
            >
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                  <Text className="text-2xl mr-3">{item.icon}</Text>
                  <Text className="text-gray-800 font-medium">{item.title}</Text>
                </View>
                <Text className="text-gray-400 text-lg">›</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          className="bg-red-50 border border-red-200 rounded-xl p-4 mb-8"
          onPress={handleLogout}
        >
          <View className="flex-row items-center justify-center">
            <Text className="text-red-600 mr-2">🚪</Text>
            <Text className="text-red-600 font-semibold">Logout</Text>
          </View>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default ProfileScreen;
