import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@env';

const supabaseUrl = SUPABASE_URL;
const supabaseAnonKey = SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types
export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category_id: string;
  stock_quantity: number;
  unit: string;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  image_url: string;
  created_at: string;
}

export interface Order {
  id: string;
  user_id: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'out_for_delivery' | 'delivered' | 'cancelled';
  total_amount: number;
  delivery_fee: number;
  delivery_address: string;
  delivery_phone: string;
  payment_method: 'mpesa' | 'card' | 'cash';
  payment_status: 'pending' | 'paid' | 'failed';
  driver_id?: string;
  estimated_delivery: string;
  created_at: string;
  updated_at: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
}

export interface Driver {
  id: string;
  user_id: string;
  vehicle_type: string;
  license_plate: string;
  phone: string;
  status: 'available' | 'busy' | 'offline';
  current_location?: {
    latitude: number;
    longitude: number;
  };
  rating: number;
  created_at: string;
}

// Auth helper functions
export const signUp = async (email: string, password: string, userData: {
  firstName: string;
  lastName: string;
  phone: string;
}) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
      },
    },
  });
  
  return { data, error };
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  return { error };
};

export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  return { user, error };
};

// Database helper functions
export const getProducts = async (categoryId?: string) => {
  let query = supabase
    .from('products')
    .select('*, categories(name)')
    .eq('is_active', true)
    .gt('stock_quantity', 0);

  if (categoryId) {
    query = query.eq('category_id', categoryId);
  }

  const { data, error } = await query;
  return { data, error };
};

export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name');
    
  return { data, error };
};

export const createOrder = async (orderData: Partial<Order>, items: Partial<OrderItem>[]) => {
  // Start a transaction
  const { data: order, error: orderError } = await supabase
    .from('orders')
    .insert(orderData)
    .select()
    .single();
    
  if (orderError) return { data: null, error: orderError };
  
  // Insert order items
  const orderItems = items.map(item => ({
    ...item,
    order_id: order.id,
  }));
  
  const { data: items_data, error: itemsError } = await supabase
    .from('order_items')
    .insert(orderItems);
    
  if (itemsError) return { data: null, error: itemsError };
  
  return { data: { order, items: items_data }, error: null };
};

export const getOrdersByUser = async (userId: string) => {
  const { data, error } = await supabase
    .from('orders')
    .select(`
      *,
      order_items(
        *,
        products(name, image_url)
      )
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false });
    
  return { data, error };
};

export const updateOrderStatus = async (orderId: string, status: Order['status']) => {
  const { data, error } = await supabase
    .from('orders')
    .update({ status, updated_at: new Date().toISOString() })
    .eq('id', orderId)
    .select()
    .single();
    
  return { data, error };
};
