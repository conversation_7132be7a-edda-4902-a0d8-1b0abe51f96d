import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, TextInput, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';

type CheckoutScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Checkout'>;

const CheckoutScreen = () => {
  const navigation = useNavigation<CheckoutScreenNavigationProp>();
  const [selectedPayment, setSelectedPayment] = useState('mpesa');
  const [deliveryAddress, setDeliveryAddress] = useState('Haraka Estate, Block A, House 12');
  const [phoneNumber, setPhoneNumber] = useState('');

  const orderItems = [
    { name: 'Bell Pepper', quantity: 1, price: 120 },
    { name: '<PERSON>', quantity: 2, price: 160 },
    { name: 'Tomatoes', quantity: 1, price: 100 },
    { name: 'Avocado', quantity: 3, price: 750 },
  ];

  const subtotal = 1130;
  const deliveryFee = 50;
  const total = subtotal + deliveryFee;

  const paymentMethods = [
    { id: 'mpesa', name: 'M-Pesa', icon: '📱', description: 'Pay with M-Pesa' },
    { id: 'card', name: 'Credit/Debit Card', icon: '💳', description: 'Pay with card' },
    { id: 'cash', name: 'Cash on Delivery', icon: '💵', description: 'Pay when delivered' },
  ];

  const handlePlaceOrder = () => {
    if (!phoneNumber && selectedPayment === 'mpesa') {
      Alert.alert('Error', 'Please enter your M-Pesa phone number');
      return;
    }

    Alert.alert(
      'Order Placed!',
      'Your order has been placed successfully. You will receive a confirmation shortly.',
      [
        {
          text: 'Track Order',
          onPress: () => navigation.navigate('OrderTracking', { orderId: 'ORD-001' })
        }
      ]
    );
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6 flex-row items-center justify-between">
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
        >
          <Text className="text-gray-600 text-lg">←</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Checkout</Text>
        <View className="w-10 h-10" />
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Delivery Address */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">Delivery Address</Text>
          <View className="bg-gray-50 rounded-xl p-4">
            <View className="flex-row items-start">
              <Text className="text-emerald-600 mr-3 text-lg">📍</Text>
              <View className="flex-1">
                <Text className="text-gray-800 font-medium">{deliveryAddress}</Text>
                <Text className="text-gray-600 text-sm mt-1">Estimated delivery: 30-45 mins</Text>
              </View>
              <TouchableOpacity>
                <Text className="text-emerald-600 font-semibold">Change</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Order Summary */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">Order Summary</Text>
          <View className="bg-gray-50 rounded-xl p-4">
            {orderItems.map((item, index) => (
              <View key={index} className="flex-row justify-between items-center mb-2">
                <Text className="text-gray-800">{item.quantity}x {item.name}</Text>
                <Text className="text-gray-800 font-medium">KSh {item.price}</Text>
              </View>
            ))}
            <View className="border-t border-gray-200 mt-3 pt-3">
              <View className="flex-row justify-between mb-2">
                <Text className="text-gray-600">Subtotal</Text>
                <Text className="text-gray-800">KSh {subtotal}</Text>
              </View>
              <View className="flex-row justify-between mb-2">
                <Text className="text-gray-600">Delivery Fee</Text>
                <Text className="text-gray-800">KSh {deliveryFee}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-lg font-semibold text-gray-800">Total</Text>
                <Text className="text-lg font-bold text-emerald-600">KSh {total}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Payment Method */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">Payment Method</Text>
          <View className="space-y-3">
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.id}
                className={`border-2 rounded-xl p-4 ${
                  selectedPayment === method.id 
                    ? 'border-emerald-600 bg-emerald-50' 
                    : 'border-gray-200 bg-white'
                }`}
                onPress={() => setSelectedPayment(method.id)}
              >
                <View className="flex-row items-center">
                  <Text className="text-2xl mr-3">{method.icon}</Text>
                  <View className="flex-1">
                    <Text className="font-semibold text-gray-800">{method.name}</Text>
                    <Text className="text-gray-600 text-sm">{method.description}</Text>
                  </View>
                  <View className={`w-6 h-6 rounded-full border-2 ${
                    selectedPayment === method.id 
                      ? 'border-emerald-600 bg-emerald-600' 
                      : 'border-gray-300'
                  }`}>
                    {selectedPayment === method.id && (
                      <View className="w-2 h-2 bg-white rounded-full self-center mt-1" />
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* M-Pesa Phone Number */}
        {selectedPayment === 'mpesa' && (
          <View className="mb-6">
            <Text className="text-lg font-semibold text-gray-800 mb-3">M-Pesa Phone Number</Text>
            <TextInput
              className="border border-gray-300 rounded-xl px-4 py-4 text-lg"
              placeholder="Enter your M-Pesa number"
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              keyboardType="phone-pad"
            />
          </View>
        )}

        {/* Special Instructions */}
        <View className="mb-8">
          <Text className="text-lg font-semibold text-gray-800 mb-3">Special Instructions</Text>
          <TextInput
            className="border border-gray-300 rounded-xl px-4 py-4 text-lg h-24"
            placeholder="Any special delivery instructions..."
            multiline
            textAlignVertical="top"
          />
        </View>
      </ScrollView>

      {/* Place Order Button */}
      <View className="bg-white border-t border-gray-200 px-6 py-4">
        <TouchableOpacity
          className="bg-emerald-600 py-4 rounded-xl"
          onPress={handlePlaceOrder}
        >
          <Text className="text-white text-lg font-semibold text-center">
            Place Order • KSh {total}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CheckoutScreen;
