import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import MapboxGL from '@rnmapbox/maps';
import * as Location from 'expo-location';

// Set Mapbox access token with fallback
const mapboxToken = process.env.MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoiaGVscHdpdGhtdWZhc2EiLCJhIjoiY21lY210dTliMDBxaDJrczVvZWdvd2EwYiJ9.8I9vddd7-hpkNoH-ITWuXw';
MapboxGL.setAccessToken(mapboxToken);

interface MapViewProps {
  showUserLocation?: boolean;
  deliveryLocation?: {
    latitude: number;
    longitude: number;
  };
  driverLocation?: {
    latitude: number;
    longitude: number;
  };
  onLocationUpdate?: (location: { latitude: number; longitude: number }) => void;
}

const MapView: React.FC<MapViewProps> = ({
  showUserLocation = true,
  deliveryLocation,
  driverLocation,
  onLocationUpdate,
}) => {
  const [userLocation, setUserLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
        getCurrentLocation();
      } else {
        console.log('Location permission denied');
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      
      const coords = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };
      
      setUserLocation(coords);
      onLocationUpdate?.(coords);
    } catch (error) {
      console.error('Error getting current location:', error);
    }
  };

  if (!locationPermission) {
    return (
      <View className="flex-1 items-center justify-center bg-gray-100">
        <Text className="text-gray-600">Location permission required</Text>
      </View>
    );
  }

  return (
    <View className="flex-1">
      <MapboxGL.MapView
        style={StyleSheet.absoluteFillObject}
        styleURL={MapboxGL.StyleURL.Street}
        zoomEnabled={true}
        scrollEnabled={true}
        pitchEnabled={false}
        rotateEnabled={false}
      >
        <MapboxGL.Camera
          centerCoordinate={
            userLocation 
              ? [userLocation.longitude, userLocation.latitude]
              : [-1.2921, 36.8219] // Default to Nairobi
          }
          zoomLevel={14}
          animationMode="flyTo"
          animationDuration={2000}
        />

        {/* User Location */}
        {showUserLocation && userLocation && (
          <MapboxGL.PointAnnotation
            id="userLocation"
            coordinate={[userLocation.longitude, userLocation.latitude]}
          >
            <View className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white" />
          </MapboxGL.PointAnnotation>
        )}

        {/* Delivery Location */}
        {deliveryLocation && (
          <MapboxGL.PointAnnotation
            id="deliveryLocation"
            coordinate={[deliveryLocation.longitude, deliveryLocation.latitude]}
          >
            <View className="w-8 h-8 bg-emerald-600 rounded-full items-center justify-center">
              <Text className="text-white text-xs">📍</Text>
            </View>
          </MapboxGL.PointAnnotation>
        )}

        {/* Driver Location */}
        {driverLocation && (
          <MapboxGL.PointAnnotation
            id="driverLocation"
            coordinate={[driverLocation.longitude, driverLocation.latitude]}
          >
            <View className="w-8 h-8 bg-orange-500 rounded-full items-center justify-center">
              <Text className="text-white text-xs">🚗</Text>
            </View>
          </MapboxGL.PointAnnotation>
        )}

        {/* Route between driver and delivery location */}
        {driverLocation && deliveryLocation && (
          <MapboxGL.ShapeSource
            id="routeSource"
            shape={{
              type: 'Feature',
              geometry: {
                type: 'LineString',
                coordinates: [
                  [driverLocation.longitude, driverLocation.latitude],
                  [deliveryLocation.longitude, deliveryLocation.latitude],
                ],
              },
            }}
          >
            <MapboxGL.LineLayer
              id="routeLine"
              style={{
                lineColor: '#059669',
                lineWidth: 3,
                lineDasharray: [2, 2],
              }}
            />
          </MapboxGL.ShapeSource>
        )}
      </MapboxGL.MapView>
    </View>
  );
};

export default MapView;
