import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';

type CartScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Cart'>;

interface CartItem {
  id: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  unit: string;
}

const CartScreen = () => {
  const navigation = useNavigation<CartScreenNavigationProp>();
  
  const [cartItems, setCartItems] = useState<CartItem[]>([
    { id: '1', name: 'Bell Pepper', price: 120, image: '🫑', quantity: 1, unit: 'kg' },
    { id: '2', name: '<PERSON>', price: 80, image: '🫚', quantity: 2, unit: 'kg' },
    { id: '3', name: 'Tomatoes', price: 100, image: '🍅', quantity: 1, unit: 'kg' },
    { id: '4', name: 'Avocado', price: 250, image: '🥑', quantity: 3, unit: 'pieces' },
  ]);

  const updateQuantity = (id: string, change: number) => {
    setCartItems(items =>
      items.map(item => {
        if (item.id === id) {
          const newQuantity = item.quantity + change;
          return newQuantity > 0 ? { ...item, quantity: newQuantity } : item;
        }
        return item;
      }).filter(item => item.quantity > 0)
    );
  };

  const removeItem = (id: string) => {
    Alert.alert(
      'Remove Item',
      'Are you sure you want to remove this item from your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Remove', onPress: () => setCartItems(items => items.filter(item => item.id !== id)) }
      ]
    );
  };

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = 50;
  const total = subtotal + deliveryFee;

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6 flex-row items-center justify-between">
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
        >
          <Text className="text-gray-600 text-lg">←</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Shopping Cart</Text>
        <View className="w-10 h-10" />
      </View>

      {cartItems.length === 0 ? (
        <View className="flex-1 items-center justify-center px-6">
          <Text className="text-6xl mb-4">🛒</Text>
          <Text className="text-xl font-semibold text-gray-800 mb-2">Your cart is empty</Text>
          <Text className="text-gray-600 text-center mb-8">
            Add some fresh products to get started
          </Text>
          <TouchableOpacity
            className="bg-emerald-600 px-8 py-4 rounded-xl"
            onPress={() => navigation.navigate('Home')}
          >
            <Text className="text-white font-semibold">Start Shopping</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <>
          <ScrollView className="flex-1 px-6">
            {/* Cart Items */}
            <View className="space-y-4">
              {cartItems.map((item) => (
                <View key={item.id} className="bg-white border border-gray-200 rounded-2xl p-4">
                  <View className="flex-row items-center">
                    {/* Product Image */}
                    <View className="w-16 h-16 bg-gray-100 rounded-2xl items-center justify-center mr-4">
                      <Text className="text-2xl">{item.image}</Text>
                    </View>

                    {/* Product Info */}
                    <View className="flex-1">
                      <Text className="text-lg font-semibold text-gray-800 mb-1">{item.name}</Text>
                      <Text className="text-emerald-600 font-bold text-lg">
                        KSh {item.price} / {item.unit}
                      </Text>
                    </View>

                    {/* Quantity Controls */}
                    <View className="flex-row items-center">
                      <TouchableOpacity
                        className="w-8 h-8 bg-gray-100 rounded-full items-center justify-center"
                        onPress={() => updateQuantity(item.id, -1)}
                      >
                        <Text className="text-gray-600 font-bold">-</Text>
                      </TouchableOpacity>
                      <Text className="mx-4 text-lg font-semibold text-gray-800">
                        {item.quantity}
                      </Text>
                      <TouchableOpacity
                        className="w-8 h-8 bg-emerald-600 rounded-full items-center justify-center"
                        onPress={() => updateQuantity(item.id, 1)}
                      >
                        <Text className="text-white font-bold">+</Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* Remove Button */}
                  <TouchableOpacity
                    className="mt-3 self-end"
                    onPress={() => removeItem(item.id)}
                  >
                    <Text className="text-red-500 text-sm">Remove</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>

            {/* Order Summary */}
            <View className="mt-8 bg-gray-50 rounded-2xl p-6">
              <Text className="text-lg font-semibold text-gray-800 mb-4">Order Summary</Text>
              
              <View className="space-y-3">
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Subtotal</Text>
                  <Text className="text-gray-800 font-medium">KSh {subtotal}</Text>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-gray-600">Delivery Fee</Text>
                  <Text className="text-gray-800 font-medium">KSh {deliveryFee}</Text>
                </View>
                <View className="border-t border-gray-200 pt-3">
                  <View className="flex-row justify-between">
                    <Text className="text-lg font-semibold text-gray-800">Total</Text>
                    <Text className="text-lg font-bold text-emerald-600">KSh {total}</Text>
                  </View>
                </View>
              </View>
            </View>
          </ScrollView>

          {/* Checkout Button */}
          <View className="bg-white border-t border-gray-200 px-6 py-4">
            <TouchableOpacity
              className="bg-emerald-600 py-4 rounded-xl"
              onPress={() => navigation.navigate('Checkout')}
            >
              <Text className="text-white text-lg font-semibold text-center">
                Proceed to Checkout • KSh {total}
              </Text>
            </TouchableOpacity>
          </View>
        </>
      )}
    </View>
  );
};

export default CartScreen;
