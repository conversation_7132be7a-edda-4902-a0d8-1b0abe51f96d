import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';
import { useProducts, useCategories } from '../../hooks/useProducts';

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { data: productsData, isLoading: productsLoading, error: productsError } = useProducts();
  const { data: categoriesData, isLoading: categoriesLoading } = useCategories();

  const products = productsData?.data || [];
  const categories = categoriesData?.data || [];

  // Product emoji mapping
  const getProductEmoji = (name: string) => {
    const emojiMap: { [key: string]: string } = {
      'tomatoes': '🍅',
      'onions': '🧅',
      'carrots': '🥕',
      'bell peppers': '🫑',
      'spinach': '🥬',
      'broccoli': '🥦',
      'bananas': '🍌',
      'apples': '🍎',
      'oranges': '🍊',
      'avocados': '🥑',
    };
    return emojiMap[name.toLowerCase()] || '🥬';
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />

      {/* Header */}
      <View className="pt-16 pb-6 px-6 bg-white">
        <View className="flex-row items-center justify-between mb-6">
          <View>
            <Text className="text-lg text-gray-600">Find all your</Text>
            <Text className="text-2xl font-bold text-gray-800">Fresh groceries</Text>
          </View>
          <TouchableOpacity
            className="w-12 h-12 bg-emerald-600 rounded-full items-center justify-center"
            onPress={() => navigation.navigate('Profile')}
          >
            <Text className="text-white text-lg">👤</Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className="flex-row items-center bg-gray-100 rounded-xl px-4 py-4">
          <Text className="text-gray-400 mr-3 text-lg">🔍</Text>
          <TextInput
            className="flex-1 text-base"
            placeholder="Search Store"
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Category Tabs */}
        <View className="flex-row mb-6">
          <TouchableOpacity
            className="bg-emerald-600 px-6 py-3 rounded-full mr-3"
            onPress={() => navigation.navigate('Categories')}
          >
            <Text className="text-white font-semibold">Vegetables</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="bg-gray-100 px-6 py-3 rounded-full mr-3"
            onPress={() => navigation.navigate('Categories')}
          >
            <Text className="text-gray-600 font-semibold">Fruits</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="bg-gray-100 px-6 py-3 rounded-full"
            onPress={() => navigation.navigate('Categories')}
          >
            <Text className="text-gray-600 font-semibold">Dairy</Text>
          </TouchableOpacity>
        </View>

        {/* Products Grid */}
        {productsLoading ? (
          <View className="flex-1 items-center justify-center py-8">
            <ActivityIndicator size="large" color="#059669" />
            <Text className="text-gray-600 mt-2">Loading products...</Text>
          </View>
        ) : productsError ? (
          <View className="flex-1 items-center justify-center py-8">
            <Text className="text-red-500 text-center">Error loading products</Text>
            <Text className="text-gray-600 text-center mt-1">Please try again later</Text>
          </View>
        ) : (
          <View className="flex-row flex-wrap justify-between">
            {products.slice(0, 8).map((product: any) => (
              <TouchableOpacity
                key={product.id}
                className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4"
                onPress={() => navigation.navigate('ProductDetails', { productId: product.id })}
              >
                <View className="items-center">
                  <View className="w-16 h-16 bg-gray-100 rounded-2xl items-center justify-center mb-3">
                    <Text className="text-3xl">{getProductEmoji(product.name)}</Text>
                  </View>
                  <Text className="font-semibold text-gray-800 text-center mb-2 text-sm">
                    {product.name}
                  </Text>
                  <Text className="text-emerald-600 font-bold text-lg">
                    KSh {product.price}
                  </Text>
                  <TouchableOpacity className="mt-3 bg-emerald-600 w-8 h-8 rounded-full items-center justify-center">
                    <Text className="text-white text-lg font-bold">+</Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}

      </ScrollView>

      {/* Bottom Navigation */}
      <View className="bg-white border-t border-gray-200 px-6 py-4">
        <View className="flex-row justify-around">
          <TouchableOpacity className="items-center">
            <Text className="text-2xl mb-1">🏠</Text>
            <Text className="text-xs text-emerald-600 font-semibold">Home</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="items-center"
            onPress={() => navigation.navigate('Cart')}
          >
            <Text className="text-2xl mb-1">🛒</Text>
            <Text className="text-xs text-gray-500">Cart</Text>
          </TouchableOpacity>
          <TouchableOpacity className="items-center">
            <Text className="text-2xl mb-1">📦</Text>
            <Text className="text-xs text-gray-500">Orders</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="items-center"
            onPress={() => navigation.navigate('Profile')}
          >
            <Text className="text-2xl mb-1">👤</Text>
            <Text className="text-xs text-gray-500">Profile</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default HomeScreen;
