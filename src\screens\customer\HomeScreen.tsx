import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';

const HomeScreen = () => {
  const navigation = useNavigation();

  const categories = [
    { id: 1, name: 'Fruits', icon: '🍎', color: 'bg-red-100' },
    { id: 2, name: 'Vegetables', icon: '🥕', color: 'bg-orange-100' },
    { id: 3, name: 'Dairy', icon: '🥛', color: 'bg-blue-100' },
    { id: 4, name: 'Meat', icon: '🥩', color: 'bg-red-200' },
    { id: 5, name: 'Grains', icon: '🌾', color: 'bg-yellow-100' },
    { id: 6, name: 'Beverages', icon: '🧃', color: 'bg-purple-100' },
  ];

  const featuredProducts = [
    { id: 1, name: 'Fresh Bananas', price: 'KSh 120/kg', image: '🍌' },
    { id: 2, name: 'Organic Tomatoes', price: 'KSh 80/kg', image: '🍅' },
    { id: 3, name: 'Fresh Milk', price: 'KSh 60/ltr', image: '🥛' },
    { id: 4, name: 'Avocados', price: 'KSh 15/piece', image: '🥑' },
  ];

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6 bg-emerald-50">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-lg text-gray-600">Good morning!</Text>
            <Text className="text-2xl font-bold text-gray-800">What's fresh today?</Text>
          </View>
          <TouchableOpacity className="w-10 h-10 bg-emerald-600 rounded-full items-center justify-center">
            <Text className="text-white text-lg">👤</Text>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className="flex-row items-center bg-white rounded-xl px-4 py-3 shadow-sm">
          <Text className="text-gray-400 mr-3">🔍</Text>
          <TextInput
            className="flex-1 text-lg"
            placeholder="Search for fresh produce..."
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      <ScrollView className="flex-1">
        {/* Location Banner */}
        <View className="mx-6 mt-4 p-4 bg-emerald-600 rounded-xl">
          <View className="flex-row items-center">
            <Text className="text-white mr-2">📍</Text>
            <View className="flex-1">
              <Text className="text-white font-semibold">Delivering to Haraka</Text>
              <Text className="text-emerald-100 text-sm">30-45 min delivery</Text>
            </View>
            <TouchableOpacity>
              <Text className="text-white underline">Change</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Categories */}
        <View className="mt-6">
          <Text className="text-xl font-bold text-gray-800 px-6 mb-4">Shop by Category</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-6">
            <View className="flex-row space-x-4">
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  className={`w-20 h-20 ${category.color} rounded-xl items-center justify-center`}
                >
                  <Text className="text-2xl mb-1">{category.icon}</Text>
                  <Text className="text-xs font-medium text-gray-700">{category.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Featured Products */}
        <View className="mt-8 px-6">
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-xl font-bold text-gray-800">Featured Products</Text>
            <TouchableOpacity>
              <Text className="text-emerald-600 font-semibold">See All</Text>
            </TouchableOpacity>
          </View>

          <View className="flex-row flex-wrap justify-between">
            {featuredProducts.map((product) => (
              <TouchableOpacity
                key={product.id}
                className="w-[48%] bg-white rounded-xl p-4 shadow-sm border border-gray-100 mb-4"
              >
                <View className="items-center">
                  <Text className="text-4xl mb-2">{product.image}</Text>
                  <Text className="font-semibold text-gray-800 text-center mb-1">
                    {product.name}
                  </Text>
                  <Text className="text-emerald-600 font-bold">{product.price}</Text>
                  <TouchableOpacity className="mt-3 bg-emerald-600 px-4 py-2 rounded-lg">
                    <Text className="text-white text-sm font-semibold">Add to Cart</Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View className="mt-6 px-6 pb-8">
          <Text className="text-xl font-bold text-gray-800 mb-4">Quick Actions</Text>
          <View className="flex-row space-x-4">
            <TouchableOpacity className="flex-1 bg-orange-100 p-4 rounded-xl items-center">
              <Text className="text-2xl mb-2">🛒</Text>
              <Text className="font-semibold text-gray-800">View Cart</Text>
            </TouchableOpacity>
            <TouchableOpacity className="flex-1 bg-blue-100 p-4 rounded-xl items-center">
              <Text className="text-2xl mb-2">📦</Text>
              <Text className="font-semibold text-gray-800">Track Order</Text>
            </TouchableOpacity>
            <TouchableOpacity className="flex-1 bg-purple-100 p-4 rounded-xl items-center">
              <Text className="text-2xl mb-2">💳</Text>
              <Text className="font-semibold text-gray-800">Payment</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default HomeScreen;
