import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigation/types';

type CategoriesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

const CategoriesScreen = () => {
  const navigation = useNavigation<CategoriesScreenNavigationProp>();
  const [selectedCategory, setSelectedCategory] = useState('vegetables');

  const categories = [
    { id: 'vegetables', name: 'Vegetables', color: 'bg-emerald-600' },
    { id: 'fruits', name: 'Fruits', color: 'bg-orange-500' },
    { id: 'dairy', name: 'Dairy', color: 'bg-blue-500' },
    { id: 'meat', name: 'Meat', color: 'bg-red-500' },
    { id: 'grains', name: 'Grains', color: 'bg-yellow-500' },
  ];

  const vegetables = [
    { id: 1, name: 'Tomatoes', price: 'KSh 120', image: '🍅', description: 'Fresh red tomatoes' },
    { id: 2, name: 'Onions', price: 'KSh 80', image: '🧅', description: 'White onions' },
    { id: 3, name: 'Carrots', price: 'KSh 100', image: '🥕', description: 'Orange carrots' },
    { id: 4, name: 'Bell Peppers', price: 'KSh 200', image: '🫑', description: 'Mixed bell peppers' },
    { id: 5, name: 'Spinach', price: 'KSh 60', image: '🥬', description: 'Fresh spinach leaves' },
    { id: 6, name: 'Broccoli', price: 'KSh 150', image: '🥦', description: 'Green broccoli' },
    { id: 7, name: 'Potatoes', price: 'KSh 70', image: '🥔', description: 'Irish potatoes' },
    { id: 8, name: 'Cabbage', price: 'KSh 90', image: '🥬', description: 'Fresh cabbage' },
  ];

  const fruits = [
    { id: 1, name: 'Bananas', price: 'KSh 120', image: '🍌', description: 'Sweet bananas' },
    { id: 2, name: 'Apples', price: 'KSh 200', image: '🍎', description: 'Red apples' },
    { id: 3, name: 'Oranges', price: 'KSh 150', image: '🍊', description: 'Juicy oranges' },
    { id: 4, name: 'Avocados', price: 'KSh 250', image: '🥑', description: 'Premium avocados' },
    { id: 5, name: 'Mangoes', price: 'KSh 180', image: '🥭', description: 'Sweet mangoes' },
    { id: 6, name: 'Pineapples', price: 'KSh 300', image: '🍍', description: 'Fresh pineapples' },
  ];

  const getCurrentProducts = () => {
    switch (selectedCategory) {
      case 'vegetables':
        return vegetables;
      case 'fruits':
        return fruits;
      default:
        return vegetables;
    }
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6 flex-row items-center justify-between">
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
        >
          <Text className="text-gray-600 text-lg">←</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Categories</Text>
        <TouchableOpacity className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center">
          <Text className="text-gray-600 text-lg">🔍</Text>
        </TouchableOpacity>
      </View>

      {/* Category Tabs */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-6 mb-6">
        <View className="flex-row space-x-3">
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              className={`px-6 py-3 rounded-full ${
                selectedCategory === category.id 
                  ? category.color 
                  : 'bg-gray-100'
              }`}
              onPress={() => setSelectedCategory(category.id)}
            >
              <Text className={`font-semibold ${
                selectedCategory === category.id 
                  ? 'text-white' 
                  : 'text-gray-600'
              }`}>
                {category.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>

      {/* Products Grid */}
      <ScrollView className="flex-1 px-6">
        <View className="flex-row flex-wrap justify-between">
          {getCurrentProducts().map((product) => (
            <TouchableOpacity
              key={product.id}
              className="w-[48%] bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-4"
              onPress={() => navigation.navigate('ProductDetails', { productId: product.id.toString() })}
            >
              <View className="items-center">
                <View className="w-20 h-20 bg-gray-100 rounded-2xl items-center justify-center mb-3">
                  <Text className="text-4xl">{product.image}</Text>
                </View>
                <Text className="font-semibold text-gray-800 text-center mb-1 text-sm">
                  {product.name}
                </Text>
                <Text className="text-gray-500 text-xs text-center mb-2">
                  {product.description}
                </Text>
                <Text className="text-emerald-600 font-bold text-lg mb-3">{product.price}</Text>
                <TouchableOpacity className="bg-emerald-600 w-8 h-8 rounded-full items-center justify-center">
                  <Text className="text-white text-lg font-bold">+</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default CategoriesScreen;
