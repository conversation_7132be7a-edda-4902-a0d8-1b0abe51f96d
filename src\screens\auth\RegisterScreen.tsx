import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { RootStackParamList } from '../../navigation/types';
import { useAuth } from '../../contexts/AuthContext';

type RegisterScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Register'>;

const RegisterSchema = Yup.object().shape({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  phone: Yup.string().required('Phone number is required'),
  password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password'),
});

const RegisterScreen = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>();
  const { signUp } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleRegister = async (values: any) => {
    setIsLoading(true);
    try {
      const { error } = await signUp(values.email, values.password, {
        firstName: values.firstName,
        lastName: values.lastName,
        phone: values.phone,
      });

      if (error) {
        Alert.alert('Registration Failed', error.message || 'Please try again.');
      } else {
        Alert.alert(
          'Success',
          'Account created successfully! Please check your email to verify your account.',
          [{ text: 'OK', onPress: () => navigation.navigate('Login') }]
        );
      }
    } catch (error) {
      Alert.alert('Registration Failed', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6">
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="mb-6"
        >
          <Text className="text-emerald-600 text-lg">← Back</Text>
        </TouchableOpacity>
        
        <Text className="text-3xl font-bold text-gray-800 mb-2">Create Account</Text>
        <Text className="text-gray-600">Join JujaFresh and start shopping</Text>
      </View>

      {/* Form */}
      <ScrollView className="flex-1 px-6">
        <Formik
          initialValues={{
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            password: '',
            confirmPassword: '',
          }}
          validationSchema={RegisterSchema}
          onSubmit={handleRegister}
        >
          {({ handleChange, handleBlur, handleSubmit, values, errors, touched }) => (
            <View className="space-y-4 pb-8">
              {/* Name Fields */}
              <View className="flex-row space-x-4">
                <View className="flex-1">
                  <Text className="text-gray-700 mb-2 font-medium">First Name</Text>
                  <TextInput
                    className="border border-gray-300 rounded-xl px-4 py-4 text-lg"
                    placeholder="First name"
                    value={values.firstName}
                    onChangeText={handleChange('firstName')}
                    onBlur={handleBlur('firstName')}
                  />
                  {touched.firstName && errors.firstName && (
                    <Text className="text-red-500 mt-1 text-sm">{errors.firstName}</Text>
                  )}
                </View>
                
                <View className="flex-1">
                  <Text className="text-gray-700 mb-2 font-medium">Last Name</Text>
                  <TextInput
                    className="border border-gray-300 rounded-xl px-4 py-4 text-lg"
                    placeholder="Last name"
                    value={values.lastName}
                    onChangeText={handleChange('lastName')}
                    onBlur={handleBlur('lastName')}
                  />
                  {touched.lastName && errors.lastName && (
                    <Text className="text-red-500 mt-1 text-sm">{errors.lastName}</Text>
                  )}
                </View>
              </View>

              {/* Email Input */}
              <View>
                <Text className="text-gray-700 mb-2 font-medium">Email</Text>
                <TextInput
                  className="border border-gray-300 rounded-xl px-4 py-4 text-lg"
                  placeholder="Enter your email"
                  value={values.email}
                  onChangeText={handleChange('email')}
                  onBlur={handleBlur('email')}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
                {touched.email && errors.email && (
                  <Text className="text-red-500 mt-1">{errors.email}</Text>
                )}
              </View>

              {/* Phone Input */}
              <View>
                <Text className="text-gray-700 mb-2 font-medium">Phone Number</Text>
                <TextInput
                  className="border border-gray-300 rounded-xl px-4 py-4 text-lg"
                  placeholder="Enter your phone number"
                  value={values.phone}
                  onChangeText={handleChange('phone')}
                  onBlur={handleBlur('phone')}
                  keyboardType="phone-pad"
                />
                {touched.phone && errors.phone && (
                  <Text className="text-red-500 mt-1">{errors.phone}</Text>
                )}
              </View>

              {/* Password Input */}
              <View>
                <Text className="text-gray-700 mb-2 font-medium">Password</Text>
                <TextInput
                  className="border border-gray-300 rounded-xl px-4 py-4 text-lg"
                  placeholder="Create a password"
                  value={values.password}
                  onChangeText={handleChange('password')}
                  onBlur={handleBlur('password')}
                  secureTextEntry
                />
                {touched.password && errors.password && (
                  <Text className="text-red-500 mt-1">{errors.password}</Text>
                )}
              </View>

              {/* Confirm Password Input */}
              <View>
                <Text className="text-gray-700 mb-2 font-medium">Confirm Password</Text>
                <TextInput
                  className="border border-gray-300 rounded-xl px-4 py-4 text-lg"
                  placeholder="Confirm your password"
                  value={values.confirmPassword}
                  onChangeText={handleChange('confirmPassword')}
                  onBlur={handleBlur('confirmPassword')}
                  secureTextEntry
                />
                {touched.confirmPassword && errors.confirmPassword && (
                  <Text className="text-red-500 mt-1">{errors.confirmPassword}</Text>
                )}
              </View>

              {/* Register Button */}
              <TouchableOpacity
                className={`py-4 px-8 rounded-xl mt-6 ${isLoading ? 'bg-gray-400' : 'bg-emerald-600'}`}
                onPress={() => handleSubmit()}
                disabled={isLoading}
              >
                <Text className="text-white text-lg font-semibold text-center">
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Text>
              </TouchableOpacity>

              {/* Login Link */}
              <View className="flex-row justify-center items-center mt-6">
                <Text className="text-gray-600">Already have an account? </Text>
                <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                  <Text className="text-emerald-600 font-semibold">Sign In</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </Formik>
      </ScrollView>
    </View>
  );
};

export default RegisterScreen;
