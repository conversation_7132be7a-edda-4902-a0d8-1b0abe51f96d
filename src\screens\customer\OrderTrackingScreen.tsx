import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../../navigation/types';
import MapView from '../../components/MapView';

type OrderTrackingScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'OrderTracking'>;
type OrderTrackingScreenRouteProp = RouteProp<RootStackParamList, 'OrderTracking'>;

const OrderTrackingScreen = () => {
  const navigation = useNavigation<OrderTrackingScreenNavigationProp>();
  const route = useRoute<OrderTrackingScreenRouteProp>();
  const [currentStep, setCurrentStep] = useState(2);

  const orderSteps = [
    { id: 1, title: 'Order Placed', description: 'Your order has been confirmed', time: '2:30 PM', completed: true },
    { id: 2, title: 'Preparing Order', description: 'We are preparing your fresh items', time: '2:45 PM', completed: true },
    { id: 3, title: 'Out for Delivery', description: 'Your order is on the way', time: '3:15 PM', completed: false },
    { id: 4, title: 'Delivered', description: 'Order delivered successfully', time: '', completed: false },
  ];

  const orderDetails = {
    orderId: route.params.orderId,
    estimatedDelivery: '3:30 PM',
    driverName: 'John Kamau',
    driverPhone: '+254 712 345 678',
    driverRating: 4.8,
    items: [
      { name: 'Bell Pepper', quantity: 1, price: 120 },
      { name: 'Ginger', quantity: 2, price: 160 },
      { name: 'Tomatoes', quantity: 1, price: 100 },
      { name: 'Avocado', quantity: 3, price: 750 },
    ],
    total: 1180
  };

  useEffect(() => {
    // Simulate order progress
    const timer = setTimeout(() => {
      if (currentStep < 4) {
        setCurrentStep(currentStep + 1);
      }
    }, 10000);

    return () => clearTimeout(timer);
  }, [currentStep]);

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="dark" />
      
      {/* Header */}
      <View className="pt-16 pb-4 px-6 flex-row items-center justify-between">
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          className="w-10 h-10 bg-gray-100 rounded-full items-center justify-center"
        >
          <Text className="text-gray-600 text-lg">←</Text>
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-800">Track Order</Text>
        <View className="w-10 h-10" />
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Order Info */}
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">Order #{orderDetails.orderId}</Text>
          <Text className="text-gray-600">Estimated delivery: {orderDetails.estimatedDelivery}</Text>
        </View>

        {/* Live Map */}
        <View className="rounded-2xl h-48 mb-6 overflow-hidden">
          <MapView
            showUserLocation={false}
            deliveryLocation={{
              latitude: -1.2921,
              longitude: 36.8219,
            }}
            driverLocation={{
              latitude: -1.2901,
              longitude: 36.8199,
            }}
          />
        </View>

        {/* Driver Info */}
        <View className="bg-emerald-50 rounded-2xl p-4 mb-6">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center flex-1">
              <View className="w-12 h-12 bg-emerald-600 rounded-full items-center justify-center mr-3">
                <Text className="text-white text-lg">👨‍🚗</Text>
              </View>
              <View className="flex-1">
                <Text className="font-semibold text-gray-800">{orderDetails.driverName}</Text>
                <View className="flex-row items-center">
                  <Text className="text-yellow-500 mr-1">⭐</Text>
                  <Text className="text-gray-600">{orderDetails.driverRating}</Text>
                </View>
              </View>
            </View>
            <TouchableOpacity className="bg-emerald-600 px-4 py-2 rounded-lg">
              <Text className="text-white font-semibold">📞 Call</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Order Progress */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-4">Order Progress</Text>
          <View className="space-y-4">
            {orderSteps.map((step, index) => (
              <View key={step.id} className="flex-row items-start">
                {/* Step Indicator */}
                <View className="items-center mr-4">
                  <View className={`w-8 h-8 rounded-full items-center justify-center ${
                    step.id <= currentStep ? 'bg-emerald-600' : 'bg-gray-200'
                  }`}>
                    {step.completed ? (
                      <Text className="text-white text-sm">✓</Text>
                    ) : step.id === currentStep ? (
                      <View className="w-3 h-3 bg-white rounded-full" />
                    ) : (
                      <View className="w-3 h-3 bg-gray-400 rounded-full" />
                    )}
                  </View>
                  {index < orderSteps.length - 1 && (
                    <View className={`w-0.5 h-8 mt-2 ${
                      step.id < currentStep ? 'bg-emerald-600' : 'bg-gray-200'
                    }`} />
                  )}
                </View>

                {/* Step Content */}
                <View className="flex-1">
                  <Text className={`font-semibold ${
                    step.id <= currentStep ? 'text-gray-800' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </Text>
                  <Text className={`text-sm ${
                    step.id <= currentStep ? 'text-gray-600' : 'text-gray-400'
                  }`}>
                    {step.description}
                  </Text>
                  {step.time && (
                    <Text className="text-xs text-gray-500 mt-1">{step.time}</Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Order Items */}
        <View className="mb-8">
          <Text className="text-lg font-semibold text-gray-800 mb-4">Order Items</Text>
          <View className="bg-gray-50 rounded-2xl p-4">
            {orderDetails.items.map((item, index) => (
              <View key={index} className="flex-row justify-between items-center mb-2">
                <Text className="text-gray-800">{item.quantity}x {item.name}</Text>
                <Text className="text-gray-800 font-medium">KSh {item.price}</Text>
              </View>
            ))}
            <View className="border-t border-gray-200 mt-3 pt-3">
              <View className="flex-row justify-between">
                <Text className="font-semibold text-gray-800">Total</Text>
                <Text className="font-bold text-emerald-600">KSh {orderDetails.total}</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View className="bg-white border-t border-gray-200 px-6 py-4">
        <View className="flex-row space-x-3">
          <TouchableOpacity className="flex-1 bg-gray-100 py-3 rounded-xl">
            <Text className="text-gray-700 font-semibold text-center">Need Help?</Text>
          </TouchableOpacity>
          <TouchableOpacity className="flex-1 bg-emerald-600 py-3 rounded-xl">
            <Text className="text-white font-semibold text-center">Rate Order</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default OrderTrackingScreen;
