{"name": "jf", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "@rnmapbox/maps": "^10.1.41", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.85.3", "expo": "~53.0.20", "expo-constants": "~17.1.7", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-notifications": "^0.31.4", "expo-secure-store": "^14.2.3", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-updates": "^0.28.17", "expo-web-browser": "~14.2.0", "formik": "^2.4.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.5", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "^2.28.0", "react-native-maps": "^1.25.3", "react-native-reanimated": "^4.0.2", "tailwindcss": "^4.1.12", "yup": "^1.7.0", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.28.3", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "typescript": "~5.8.3"}, "private": true}